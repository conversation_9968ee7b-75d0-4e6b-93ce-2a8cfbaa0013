# -*- coding: utf-8 -*-
"""
小红书 API 使用示例

本示例演示如何使用 jss_api_extend 包获取小红书笔记详情和评论。
"""

import os
import sys

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from jss_api_extend import XhsDetailHandler, XhsCommentHandler
from jss_api_extend.common import Env


def xhs_detail_example():
    """小红书笔记详情获取示例"""
    print("=== 小红书笔记详情获取示例 ===")
    
    # 初始化日志
    logger = Env().get_main_logger()
    
    # 创建小红书详情处理器
    xhs_handler = XhsDetailHandler(logger)
    
    # 示例笔记ID
    note_id = "6863e1450000000024008d2b"
    
    try:
        print(f"正在获取笔记ID {note_id} 的详情...")
        
        # 获取笔记详情
        note_detail = xhs_handler.query_article_detail(note_id)
        
        if note_detail:
            print("✅ 成功获取笔记详情:")
            print(f"  标题: {note_detail.title}")
            print(f"  作者: {note_detail.author_name}")
            print(f"  点赞数: {note_detail.like_count}")
            print(f"  评论数: {note_detail.comment_count}")
            print(f"  收藏数: {note_detail.collect_count}")
            print(f"  分享数: {note_detail.share_count}")
            print(f"  发布时间: {note_detail.publish_time}")
            print(f"  笔记链接: {note_detail.work_url}")
            
            # 显示标签信息
            if hasattr(note_detail, 'tags') and note_detail.tags:
                print(f"  标签: {', '.join(note_detail.tags)}")
                
        else:
            print("❌ 未能获取笔记详情")
            
    except Exception as e:
        print(f"❌ 获取笔记详情时发生错误: {e}")


def xhs_comment_example():
    """小红书评论获取示例"""
    print("\n=== 小红书评论获取示例 ===")
    
    # 初始化日志
    logger = Env().get_main_logger()
    
    # 创建小红书评论处理器
    comment_handler = XhsCommentHandler(logger)
    
    # 示例笔记ID
    note_id = "6863e1450000000024008d2b"
    
    try:
        print(f"正在获取笔记ID {note_id} 的评论...")
        
        # 获取评论 (获取前50条)
        success, all_comments, top_comments = comment_handler.query_article_comment(
            note_id, count=50
        )
        
        if success:
            print("✅ 成功获取评论:")
            print(f"  总评论数: {len(all_comments) if all_comments else 0}")
            print(f"  热门评论数: {len(top_comments) if top_comments else 0}")
            
            # 显示前几条评论
            if all_comments:
                print("\n  前5条评论:")
                for i, comment in enumerate(all_comments[:5]):
                    print(f"    {i+1}. {comment.author_name}: {comment.content}")
                    print(f"       点赞数: {comment.like_count}, 时间: {comment.publish_time}")
                    
            if top_comments:
                print("\n  热门评论:")
                for i, comment in enumerate(top_comments[:3]):
                    print(f"    {i+1}. {comment.author_name}: {comment.content}")
                    print(f"       点赞数: {comment.like_count}, 时间: {comment.publish_time}")
        else:
            print("❌ 未能获取评论")
            
    except Exception as e:
        print(f"❌ 获取评论时发生错误: {e}")


def main():
    """主函数"""
    print("🚀 JSS API Extension - 小红书功能演示")
    print("=" * 50)
    
    # 运行小红书详情获取示例
    xhs_detail_example()
    
    # 运行小红书评论获取示例
    xhs_comment_example()
    
    print("\n✨ 演示完成!")


if __name__ == "__main__":
    # 检查环境变量
    if not os.getenv('TIKHUB_API_KEY'):
        print("⚠️  警告: 未设置 TIKHUB_API_KEY 环境变量")
        print("请设置环境变量后再运行示例:")
        print("export TIKHUB_API_KEY='your_api_key'")
        print("export TIKHUB_BASE_URL='https://api.tikhub.io'")
        sys.exit(1)
    
    # 运行主函数
    main()
