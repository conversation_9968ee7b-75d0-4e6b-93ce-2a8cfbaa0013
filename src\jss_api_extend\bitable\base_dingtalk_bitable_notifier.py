# -*- coding: utf-8 -*-

import json
import time
from typing import Optional

from alibabacloud_dingtalk.notable_1_0 import models as dingtalknotable__1__0_models
from alibabacloud_dingtalk.notable_1_0.client import Client as dingtalknotable_1_0_Client
from alibabacloud_dingtalk.notable_1_0.models import *
from alibabacloud_dingtalk.oauth2_1_0 import models as dingtalkoauth_2__1__0_models
from alibabacloud_dingtalk.oauth2_1_0.client import Client as dingtalkoauth2_1_0Client
from alibabacloud_tea_openapi import models as open_api_models
from alibabacloud_tea_util import models as util_models

from .dingtalk_alerter import DingTalkAlerter
from ..utils.http_utils import JSSHttpUtils


class BaseDingTalkBitableNotifier:
    """
    条件参考多维表官方连接器平台
    参考链接: https://open.dingtalk.com/document/connector/multidimensional-table-parameter-description
    使用方式：
    1. 继承此类
    2. 实现 get_operator_id() 方法
    3. 直接调用 query_records/update_records/insert_records 方法
    """

    def __init__(self, logger_, app_key, app_secret, agent_id, operator_id):

        self.logger_ = logger_
        self.client = self._create_client()
        self.access_token = None
        self.expire_time = int(time.time())
        self.headers = {
            "accept": "application/json"
        }

        self.bitable_url = "https://api.dingtalk.com/v1.0/notable/bases/"

        self.app_key = app_key
        self.app_secret = app_secret
        self.agent_id = agent_id
        self.operator_id = operator_id
        self.ding_talk_alert = DingTalkAlerter()

    def set_operator_id(self, operator_id):
        self.operator_id = operator_id

    # ==================== 通用功能实现 ====================

    def _create_client(self) -> dingtalkoauth2_1_0Client:
        """
        使用 Token 初始化账号Client
        @return: Client
        @throws Exception
        """
        config = open_api_models.Config()
        config.protocol = 'https'
        config.region_id = 'central'
        return dingtalkoauth2_1_0Client(config)

    def get_access_token(self):
        if self.access_token is None or int(time.time()) >= self.expire_time:
            self.__refresh_access_token()

        return self.access_token

    def __refresh_access_token(self):
        flag, access_token, expire_in = self._query_new_access_token()
        if flag:
            self.access_token = access_token
            self.expire_time = int(time.time()) + expire_in - 60 * 5

    def _query_new_access_token(self):
        get_access_token_request = dingtalkoauth_2__1__0_models.GetAccessTokenRequest(
            app_key=self.app_key,
            app_secret=self.app_secret
        )

        try:
            response: dingtalkoauth_2__1__0_models.GetAccessTokenResponse = self.client.get_access_token(
                get_access_token_request)
            if response.status_code != 200:
                return False, None, None

            body: dingtalkoauth_2__1__0_models.GetAccessTokenResponseBody = response.body
            return True, body.access_token, body.expire_in
        except Exception as err:
            self.logger_.error(f"dingtalk get_access_token error={err}")

    def get_user_id_by_union_id(self, union_id):
        access_token = self.get_access_token()
        url = 'https://oapi.dingtalk.com/topapi/user/getbyunionid?access_token=' + access_token
        body = {
            "unionid": union_id
        }

        try:
            response = JSSHttpUtils.post(url, headers=self.headers, data=body)
            self.logger_.info(f'get_user_id_by_union_id={response.text}')
            json_str = response.content.decode('utf-8')
            data = json.loads(json_str)
            return data.get('result').get('userid')
        except Exception as e:
            self.logger_.error(e)

    def send_message(self, union_id, message):
        user_id = self.get_user_id_by_union_id(union_id)

        url = "https://oapi.dingtalk.com/topapi/message/corpconversation/asyncsend_v2?access_token=" + self.get_access_token()
        body = {
            "agent_id": self.agent_id,
            "msg": {
                "msgtype": "text",
                "text": {
                    "content": message
                }
            },
            "userid_list": user_id
        }

        try:
            response = JSSHttpUtils.post(url, headers=self.headers, data=json.dumps(body))
            self.logger_.info(f"send_message={response.text}")
        except Exception as e:
            self.logger_.error(f"{e}")

    def _create_bitable_client(self) -> dingtalknotable_1_0_Client:
        """
        使用 Token 初始化账号Client
        @return: Client
        @throws Exception
        """
        config = open_api_models.Config()
        config.protocol = 'https'
        config.region_id = 'central'
        return dingtalknotable_1_0_Client(config)

    # ==================== 简单易用的查询方法 ====================

    def query_records(self,
                      dentry_uuid: str,
                      id_or_name: str,
                      filter_conditions: Optional[List[Dict[str, Any]]] = None,
                      max_results: int = 100) -> Optional[List[Dict[str, Any]]]:
        """
        查询多维表记录 - 简单易用的接口

        Args:
            dentry_uuid: 多维表的base_id
            id_or_name: 工作表的ID或名称
            filter_conditions: 过滤条件列表，格式：
                [
                    {"field": "字段名", "operator": "操作符", "value": ["值"]},
                    {"field": "状态", "operator": "equal", "value": ["进行中"]}
                ]
            max_results: 每页最大结果数，默认100

        Returns:
            记录列表，每个记录包含 id 和 fields
        """
        try:
            dingtalk_notable: dingtalknotable_1_0_Client = self._create_bitable_client()
            headers = dingtalknotable__1__0_models.ListRecordsHeaders()
            headers.x_acs_dingtalk_access_token = self.get_access_token()

            # 构建过滤条件
            filter_obj = None
            if filter_conditions:
                conditions = []
                for condition in filter_conditions:
                    filter_condition = ListRecordsRequestFilterConditions()
                    filter_condition.field = condition["field"]
                    filter_condition.operator = condition["operator"]
                    filter_condition.value = condition["value"]
                    conditions.append(filter_condition)

                filter_obj = ListRecordsRequestFilter()
                filter_obj.combination = "and"
                filter_obj.conditions = conditions

            has_more = True
            next_token = None
            all_records = []

            request = dingtalknotable__1__0_models.ListRecordsRequest()
            request.operator_id = self.operator_id
            request.max_results = max_results
            request.filter = filter_obj

            while has_more:
                request.next_token = next_token
                response: dingtalknotable__1__0_models.ListRecordsResponse = dingtalk_notable.list_records_with_options(
                    base_id=dentry_uuid,
                    sheet_id_or_name=id_or_name,
                    request=request,
                    headers=headers,
                    runtime=util_models.RuntimeOptions())

                if response.status_code != 200:
                    self.logger_.error(f"查询多维表失败: status_code={response.status_code}")
                    break

                has_more = response.body.has_more
                next_token = response.body.next_token
                records = response.body.records

                for record in records:
                    all_records.append({
                        "id": record.id,
                        "fields": record.fields,
                        "created_time": record.created_time,
                        "last_modified_time": record.last_modified_time
                    })

            self.logger_.info(f"成功查询到 {len(all_records)} 条记录")
            return all_records

        except Exception as e:
            self.logger_.error(f"查询多维表记录时发生异常: {str(e)}")
            return None

    # ==================== 简单易用的更新方法 ====================

    def update_records(self,
                       dentry_uuid: str,
                       id_or_name: str,
                       record_updates: List[Dict[str, Any]]) -> bool:
        """
        更新多维表记录 - 简单易用的接口

        Args:
            dentry_uuid: 多维表的base_id
            id_or_name: 工作表的ID或名称
            record_updates: 更新数据列表，格式:
                [
                    {"record_id": "记录ID", "fields": {"字段名": "新值"}},
                    {"record_id": "记录ID2", "fields": {"状态": "已完成"}}
                ]

        Returns:
            更新是否成功
        """
        try:
            dingtalk_notable: dingtalknotable_1_0_Client = self._create_bitable_client()
            headers = dingtalknotable__1__0_models.UpdateRecordsHeaders()
            headers.x_acs_dingtalk_access_token = self.get_access_token()

            records = []
            for update_data in record_updates:
                record = dingtalknotable__1__0_models.UpdateRecordsRequestRecords()
                record.id = update_data["record_id"]
                record.fields = update_data["fields"]
                records.append(record)

            request = dingtalknotable__1__0_models.UpdateRecordsRequest()
            request.operator_id = self.operator_id
            request.records = records

            response: dingtalknotable__1__0_models.UpdateRecordsResponse = dingtalk_notable.update_records_with_options(
                base_id=dentry_uuid,
                sheet_id_or_name=id_or_name,
                request=request,
                headers=headers,
                runtime=util_models.RuntimeOptions())

            if response.status_code != 200:
                self.logger_.error(
                    f"批量更新多维表失败: base_id={dentry_uuid}, sheet={id_or_name}, status_code={response.status_code}")
                self.ding_talk_alert.send(f"更新多维表={dentry_uuid} 失败，状态码: {response.status_code}")
                return False

            updated_count = len(record_updates)
            self.logger_.info(f"成功更新 {updated_count} 条记录到多维表: base_id={dentry_uuid}, sheet={id_or_name}")
            return True

        except Exception as e:
            self.logger_.error(f"更新多维表数据时发生异常: {str(e)}")
            self.ding_talk_alert.send(f"更新多维表={dentry_uuid} 异常: {str(e)}")
            return False

    # ==================== 简单易用的插入方法 ====================

    def insert_records(self,
                       dentry_uuid: str,
                       id_or_name: str,
                       records_data: List[Dict[str, Any]]) -> bool:
        """
        插入多维表记录 - 简单易用的接口

        Args:
            dentry_uuid: 多维表的base_id
            id_or_name: 工作表的ID或名称
            records_data: 要插入的记录数据列表，格式:
                [
                    {"字段名1": "值1", "字段名2": "值2"},
                    {"字段名1": "值3", "字段名2": "值4"}
                ]

        Returns:
            插入是否成功
        """
        try:
            dingtalk_notable: dingtalknotable_1_0_Client = self._create_bitable_client()
            headers = dingtalknotable__1__0_models.InsertRecordsHeaders()
            headers.x_acs_dingtalk_access_token = self.get_access_token()

            # 构建记录列表
            records = []
            for record_data in records_data:
                record = dingtalknotable__1__0_models.InsertRecordsRequestRecords()
                record.fields = record_data
                records.append(record)

            # 创建请求对象
            request = dingtalknotable__1__0_models.InsertRecordsRequest()
            request.operator_id = self.operator_id
            request.records = records

            # 发送批量插入请求 (同步版本)
            response: dingtalknotable__1__0_models.InsertRecordsResponse = dingtalk_notable.insert_records_with_options(
                base_id=dentry_uuid,
                sheet_id_or_name=id_or_name,
                request=request,
                headers=headers,
                runtime=util_models.RuntimeOptions())

            # 检查响应状态
            if response.status_code != 200:
                self.logger_.error(
                    f"批量插入多维表失败: base_id={dentry_uuid}, sheet={id_or_name}, status_code={response.status_code}")
                self.ding_talk_alert.send(f"批量插入多维表={dentry_uuid} 失败，状态码: {response.status_code}")
                return False

            # 记录成功信息
            inserted_count = len(records_data)
            self.logger_.info(f"成功插入 {inserted_count} 条记录到多维表: base_id={dentry_uuid}, sheet={id_or_name}")

            return True

        except Exception as e:
            self.logger_.error(f"插入多维表数据时发生异常: {str(e)}")
            self.ding_talk_alert.send(f"插入多维表={dentry_uuid} 异常: {str(e)}")
            return False

    async def insert_records_async(self,
                                   dentry_uuid: str,
                                   id_or_name: str,
                                   records_data: List[Dict[str, Any]]) -> bool:
        """
        异步插入多维表记录

        Args:
            dentry_uuid: 多维表的base_id
            id_or_name: 工作表的ID或名称
            records_data: 要插入的记录数据列表

        Returns:
            插入是否成功
        """
        try:
            dingtalk_notable: dingtalknotable_1_0_Client = self._create_bitable_client()
            headers = dingtalknotable__1__0_models.InsertRecordsHeaders()
            headers.x_acs_dingtalk_access_token = self.get_access_token()

            # 构建记录列表
            records = []
            for record_data in records_data:
                record = dingtalknotable__1__0_models.InsertRecordsRequestRecords()
                record.fields = record_data
                records.append(record)

            # 创建请求对象
            request = dingtalknotable__1__0_models.InsertRecordsRequest()
            request.operator_id = self.operator_id
            request.records = records

            # 发送批量插入请求 (异步版本)
            response: dingtalknotable__1__0_models.InsertRecordsResponse = await dingtalk_notable.insert_records_with_options_async(
                base_id=dentry_uuid,
                sheet_id_or_name=id_or_name,
                request=request,
                headers=headers,
                runtime=util_models.RuntimeOptions())

            # 检查响应状态
            if response.status_code != 200:
                self.logger_.error(
                    f"批量插入多维表失败: base_id={dentry_uuid}, sheet={id_or_name}, status_code={response.status_code}")
                self.ding_talk_alert.send(f"批量插入多维表={dentry_uuid} 失败，状态码: {response.status_code}")
                return False

            # 记录成功信息
            inserted_count = len(records_data)
            self.logger_.info(
                f"成功异步插入 {inserted_count} 条记录到多维表: base_id={dentry_uuid}, sheet={id_or_name}")

            return True

        except Exception as e:
            self.logger_.error(f"异步插入多维表数据时发生异常: {str(e)}")
            self.ding_talk_alert.send(f"异步插入多维表={dentry_uuid} 异常: {str(e)}")
            return False
